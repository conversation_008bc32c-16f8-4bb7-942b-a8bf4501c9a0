# AI SEO

<img src="Screenshot.png" alt="AI SEO screenshot" />

This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Features

- Modern, dark design
- Animated with Framer Motion
- Responsive layout
- SEO-optimized
- Cross-Browser Compatibility
- Accessibility
- Easy to Customize

## Getting Started

First, To install the project dependencies, run:

```bash
npm install
# or
yarn install
# or
pnpm install
# or
bun install
```

## Usage

After installation, you can start the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load DM_Sans, a custom Google Font.

## Design

You can view the design for this project on Figma: [AI Startup Landing Page](https://www.figma.com/design/XpZQogjrnUSWYceMccagvu/AI-Startup-Landing-Page?node-id=4007-684&t=7L89agO6vZJulren-1)

## Contributing

Contributions are welcome! Please read the [contributing guidelines](CONTRIBUTING.md) first.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
