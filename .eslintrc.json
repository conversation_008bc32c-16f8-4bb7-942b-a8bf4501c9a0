{"root": true, "extends": ["next/core-web-vitals", "eslint:recommended"], "overrides": [{"files": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"]}], "rules": {"no-unused-vars": "warn", "no-console": "warn", "no-debugger": "error", "no-alert": "error", "no-eval": "error", "no-empty": "error", "no-undef": "warn", "no-var": "error", "eqeqeq": ["error", "always"], "curly": "error", "strict": ["error", "global"]}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}}